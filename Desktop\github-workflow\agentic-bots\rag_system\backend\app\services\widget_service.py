"""
Service for managing embeddable chat widgets.
"""
from typing import List, Optional, Dict, Any
import uuid
import secrets
from datetime import datetime
from urllib.parse import urlparse

from app.models.schemas import (
    ChatWidget, ChatWidgetCreate, ChatWidgetUpdate,
    PaginatedResponse, WidgetEmbed
)
from app.database import get_supabase_client
import structlog

logger = structlog.get_logger()


class WidgetService:
    """Service for managing embeddable chat widgets."""
    
    async def create_widget(self, widget_data: ChatWidgetCreate) -> ChatWidget:
        """Create a new chat widget."""
        try:
            supabase_client = await get_supabase_client()
            
            widget_dict = widget_data.model_dump()
            widget_dict["id"] = str(uuid.uuid4())
            widget_dict["chatbot_id"] = str(widget_data.chatbot_id)
            widget_dict["api_key"] = self._generate_api_key()
            widget_dict["created_at"] = datetime.utcnow().isoformat()
            widget_dict["updated_at"] = datetime.utcnow().isoformat()
            
            response = supabase_client.table("chat_widgets").insert(widget_dict).execute()
            
            if not response.data:
                raise Exception("Failed to create widget")
            
            return ChatWidget(**response.data[0])
            
        except Exception as e:
            logger.error(f"Error creating widget: {e}")
            raise
    
    async def get_widget(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> Optional[ChatWidget]:
        """Get a specific widget by ID."""
        try:
            supabase_client = await get_supabase_client()
            
            response = supabase_client.table("chat_widgets").select("*").eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting widget: {e}")
            raise
    
    async def get_widget_by_api_key(self, api_key: str) -> Optional[ChatWidget]:
        """Get a widget by its API key."""
        try:
            supabase_client = await get_supabase_client()
            
            response = supabase_client.table("chat_widgets").select("*").eq("api_key", api_key).eq("is_active", True).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting widget by API key: {e}")
            raise
    
    async def get_chatbot_widgets(
        self, 
        chatbot_id: uuid.UUID, 
        skip: int = 0, 
        limit: int = 10,
        active_only: bool = True
    ) -> PaginatedResponse:
        """Get all widgets for a chatbot with pagination."""
        try:
            supabase_client = await get_supabase_client()
            
            # Build query
            query = supabase_client.table("chat_widgets").select("*", count="exact").eq("chatbot_id", str(chatbot_id))
            
            if active_only:
                query = query.eq("is_active", True)
            
            # Get total count
            count_response = query.execute()
            total = len(count_response.data) if count_response.data else 0
            
            # Get paginated results
            query = query.range(skip, skip + limit - 1).order("created_at", desc=True)
            response = query.execute()
            
            widgets = [ChatWidget(**widget) for widget in response.data] if response.data else []
            
            return PaginatedResponse(
                items=widgets,
                total=total,
                page=skip // limit + 1,
                per_page=limit,
                pages=(total + limit - 1) // limit
            )
            
        except Exception as e:
            logger.error(f"Error getting chatbot widgets: {e}")
            raise
    
    async def update_widget(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID, widget_data: ChatWidgetUpdate) -> Optional[ChatWidget]:
        """Update an existing widget."""
        try:
            supabase_client = await get_supabase_client()
            
            # Only update fields that are provided
            update_dict = {k: v for k, v in widget_data.model_dump().items() if v is not None}
            
            if not update_dict:
                # If no fields to update, return current widget
                return await self.get_widget(widget_id, chatbot_id)
            
            update_dict["updated_at"] = datetime.utcnow().isoformat()
            
            response = supabase_client.table("chat_widgets").update(update_dict).eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error updating widget: {e}")
            raise
    
    async def delete_widget(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> bool:
        """Delete a widget (soft delete by setting is_active=False)."""
        try:
            supabase_client = await get_supabase_client()
            
            response = supabase_client.table("chat_widgets").update({
                "is_active": False,
                "updated_at": datetime.utcnow().isoformat()
            }).eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            return bool(response.data)
            
        except Exception as e:
            logger.error(f"Error deleting widget: {e}")
            raise
    
    async def regenerate_api_key(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> Optional[ChatWidget]:
        """Regenerate API key for a widget."""
        try:
            supabase_client = await get_supabase_client()
            
            new_api_key = self._generate_api_key()
            
            response = supabase_client.table("chat_widgets").update({
                "api_key": new_api_key,
                "updated_at": datetime.utcnow().isoformat()
            }).eq("id", str(widget_id)).eq("chatbot_id", str(chatbot_id)).execute()
            
            if response.data:
                return ChatWidget(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error regenerating API key: {e}")
            raise
    
    async def validate_widget_access(self, api_key: str, origin: str) -> tuple[bool, Optional[ChatWidget]]:
        """Validate if a request from a specific origin is allowed for the widget."""
        try:
            widget = await self.get_widget_by_api_key(api_key)
            if not widget:
                return False, None
            
            # Check if widget is active
            if not widget.is_active:
                return False, widget
            
            # Check allowed domains
            if not widget.allowed_domains:
                # If no domains specified, allow all
                return True, widget
            
            # Extract domain from origin
            try:
                parsed_origin = urlparse(origin)
                origin_domain = parsed_origin.netloc.lower()
            except Exception:
                return False, widget
            
            # Check if domain is in allowed list
            allowed = any(
                self._domain_matches(origin_domain, allowed_domain.lower())
                for allowed_domain in widget.allowed_domains
            )
            
            return allowed, widget
            
        except Exception as e:
            logger.error(f"Error validating widget access: {e}")
            return False, None
    
    def _domain_matches(self, origin_domain: str, allowed_domain: str) -> bool:
        """Check if origin domain matches allowed domain (supports wildcards)."""
        # Exact match
        if origin_domain == allowed_domain:
            return True
        
        # Wildcard subdomain match
        if allowed_domain.startswith("*."):
            base_domain = allowed_domain[2:]
            return origin_domain.endswith(f".{base_domain}") or origin_domain == base_domain
        
        # Subdomain match
        if origin_domain.endswith(f".{allowed_domain}"):
            return True
        
        return False
    
    def _generate_api_key(self) -> str:
        """Generate a secure API key for the widget."""
        return f"widget_{secrets.token_urlsafe(32)}"
    
    async def get_widget_embed_code(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID) -> Dict[str, str]:
        """Generate embed code for the widget."""
        try:
            widget = await self.get_widget(widget_id, chatbot_id)
            if not widget:
                raise ValueError(f"Widget {widget_id} not found")
            
            # Basic embed script
            embed_script = f"""
<!-- AI Chatbot Widget -->
<iframe
    src="http://localhost:8000/widget/{widget.api_key}"
    width="400"
    height="600"
    frameborder="0"
    style="position: fixed; bottom: 20px; right: 20px; z-index: 9999; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.15);">
</iframe>
"""
            
            # Script tag version
            script_code = f"""
<!-- AI Chatbot Widget Script -->
<script>
(function() {{
    const iframe = document.createElement('iframe');
    iframe.src = 'http://localhost:8000/widget/{widget.api_key}';
    iframe.style.cssText = 'position: fixed; bottom: 20px; right: 20px; width: 400px; height: 600px; border: none; border-radius: 10px; box-shadow: 0 4px 20px rgba(0,0,0,0.15); z-index: 9999;';
    iframe.frameBorder = '0';
    document.body.appendChild(iframe);
}})();
</script>
"""

            # React component code
            react_code = f"""
// React Component for AI Chat Widget
function AIChatWidget() {{
    return (
        <iframe
            src="http://localhost:8000/widget/{widget.api_key}"
            style={{{{
                position: 'fixed',
                bottom: '20px',
                right: '20px',
                width: '400px',
                height: '600px',
                border: 'none',
                borderRadius: '10px',
                boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                zIndex: 9999
            }}}}
            frameBorder="0"
        />
    );
}}
"""

            # Configuration for different frameworks
            configs = {
                "script_tag": script_code.strip(),
                "iframe_tag": embed_script.strip(),
                "react_component": react_code.strip(),
                "configuration": widget.widget_config
            }
            
            return configs
            
        except Exception as e:
            logger.error(f"Error generating embed code: {e}")
            raise
    
    async def get_widget_analytics(self, widget_id: uuid.UUID, chatbot_id: uuid.UUID, days: int = 30) -> Dict[str, Any]:
        """Get analytics data for a widget."""
        try:
            # This would typically query analytics data from a separate analytics service
            # For now, return mock data structure
            
            analytics = {
                "widget_id": str(widget_id),
                "period_days": days,
                "total_conversations": 0,
                "total_messages": 0,
                "average_session_duration": 0,
                "popular_questions": [],
                "daily_stats": [],
                "domain_stats": {},
                "action_executions": 0
            }
            
            # In a real implementation, you would:
            # 1. Query conversation logs
            # 2. Calculate metrics
            # 3. Return real analytics data
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error getting widget analytics: {e}")
            raise
    
    async def customize_widget_appearance(
        self, 
        widget_id: uuid.UUID, 
        chatbot_id: uuid.UUID, 
        appearance_config: Dict[str, Any]
    ) -> Optional[ChatWidget]:
        """Update widget appearance configuration."""
        try:
            widget = await self.get_widget(widget_id, chatbot_id)
            if not widget:
                return None
            
            # Merge appearance config with existing widget config
            current_config = widget.widget_config.copy()
            current_config.update({
                "appearance": appearance_config,
                "updated_at": datetime.utcnow().isoformat()
            })
            
            update_data = ChatWidgetUpdate(widget_config=current_config)
            return await self.update_widget(widget_id, chatbot_id, update_data)
            
        except Exception as e:
            logger.error(f"Error customizing widget appearance: {e}")
            raise
    
    def get_default_widget_config(self) -> Dict[str, Any]:
        """Get default widget configuration."""
        return {
            "appearance": {
                "theme": "light",
                "primaryColor": "#007bff",
                "secondaryColor": "#6c757d",
                "fontFamily": "Arial, sans-serif",
                "borderRadius": "8px",
                "position": "bottom-right",
                "width": "350px",
                "height": "500px"
            },
            "behavior": {
                "autoOpen": False,
                "autoOpenDelay": 5000,
                "showWelcomeMessage": True,
                "welcomeMessage": "Hello! How can I help you today?",
                "enableNotifications": True,
                "enableSoundNotifications": False
            },
            "features": {
                "enableFileUpload": False,
                "enableEmailTranscript": True,
                "enableTypingIndicator": True,
                "enableReadReceipts": False,
                "enableMessageHistory": True
            },
            "branding": {
                "showPoweredBy": True,
                "logoUrl": None,
                "companyName": None
            }
        }


# Global widget service instance
widget_service = WidgetService()


async def get_widget_service() -> WidgetService:
    """Dependency to get widget service."""
    return widget_service
