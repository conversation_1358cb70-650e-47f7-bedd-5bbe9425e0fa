"""
Chat widgets API endpoints for embeddable widgets.
"""
from typing import Dict, Any
import uuid
from fastapi import APIRouter, Depends, HTTPException

from app.models.schemas import (
    ChatWidget, ChatWidgetCreate, ChatWidgetCreateRequest, ChatWidgetUpdate,
    PaginatedResponse, APIResponse
)
from app.services.widget_service import WidgetService, get_widget_service
import structlog

logger = structlog.get_logger()

router = APIRouter(prefix="/chatbots/{chatbot_id}/widgets", tags=["widgets"])

# Public widget router (no chatbot_id prefix needed)
public_router = APIRouter(prefix="/widgets", tags=["public-widgets"])


@router.post("/", response_model=ChatWidget)
async def create_widget(
    chatbot_id: uuid.UUID,
    widget_data: ChatWidgetCreateRequest,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Create a new chat widget for a chatbot."""
    try:
        # Create the full widget data with chatbot_id
        full_widget_data = ChatWidgetCreate(
            chatbot_id=chatbot_id,
            allowed_domains=widget_data.allowed_domains,
            widget_config=widget_data.widget_config,
            is_active=widget_data.is_active
        )

        # Set default config if not provided
        if not full_widget_data.widget_config:
            full_widget_data.widget_config = widget_service.get_default_widget_config()

        widget = await widget_service.create_widget(full_widget_data)
        return widget

    except Exception as e:
        logger.error(f"Error creating widget: {e}")
        raise HTTPException(status_code=500, detail="Error creating widget")


@router.get("/", response_model=PaginatedResponse)
async def get_chatbot_widgets(
    chatbot_id: uuid.UUID,
    skip: int = 0,
    limit: int = 10,
    active_only: bool = True,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Get all widgets for a chatbot with pagination."""
    try:
        result = await widget_service.get_chatbot_widgets(
            chatbot_id=chatbot_id,
            skip=skip,
            limit=limit,
            active_only=active_only
        )
        return result
    except Exception as e:
        logger.error(f"Error getting chatbot widgets: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving widgets")


@router.get("/{widget_id}", response_model=ChatWidget)
async def get_widget(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Get a specific widget by ID."""
    try:
        widget = await widget_service.get_widget(widget_id, chatbot_id)
        if not widget:
            raise HTTPException(status_code=404, detail="Widget not found")
        return widget
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting widget: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving widget")


@router.put("/{widget_id}", response_model=ChatWidget)
async def update_widget(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    widget_data: ChatWidgetUpdate,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Update an existing widget."""
    try:
        widget = await widget_service.update_widget(widget_id, chatbot_id, widget_data)
        if not widget:
            raise HTTPException(status_code=404, detail="Widget not found")
        return widget
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating widget: {e}")
        raise HTTPException(status_code=500, detail="Error updating widget")


@router.delete("/{widget_id}")
async def delete_widget(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Delete a widget (soft delete)."""
    try:
        success = await widget_service.delete_widget(widget_id, chatbot_id)
        if not success:
            raise HTTPException(status_code=404, detail="Widget not found")
        
        return APIResponse(
            success=True,
            message="Widget deleted successfully"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting widget: {e}")
        raise HTTPException(status_code=500, detail="Error deleting widget")


@router.post("/{widget_id}/regenerate_key", response_model=ChatWidget)
async def regenerate_api_key(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Regenerate API key for a widget."""
    try:
        widget = await widget_service.regenerate_api_key(widget_id, chatbot_id)
        if not widget:
            raise HTTPException(status_code=404, detail="Widget not found")
        return widget
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error regenerating API key: {e}")
        raise HTTPException(status_code=500, detail="Error regenerating API key")


@router.get("/{widget_id}/embed_code")
async def get_widget_embed_code(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Generate embed code for the widget."""
    try:
        embed_codes = await widget_service.get_widget_embed_code(widget_id, chatbot_id)

        return APIResponse(
            success=True,
            message="Embed code generated successfully",
            data=embed_codes
        )

    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error generating embed code: {e}")
        raise HTTPException(status_code=500, detail="Error generating embed code")


@router.get("/{widget_id}/analytics")
async def get_widget_analytics(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    days: int = 30,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Get analytics data for a widget."""
    try:
        analytics = await widget_service.get_widget_analytics(widget_id, chatbot_id, days)
        
        return APIResponse(
            success=True,
            message="Analytics data retrieved successfully",
            data=analytics
        )
        
    except Exception as e:
        logger.error(f"Error getting widget analytics: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving analytics")


@router.post("/{widget_id}/customize")
async def customize_widget_appearance(
    chatbot_id: uuid.UUID,
    widget_id: uuid.UUID,
    appearance_config: Dict[str, Any],
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Update widget appearance configuration."""
    try:
        widget = await widget_service.customize_widget_appearance(
            widget_id, chatbot_id, appearance_config
        )
        
        if not widget:
            raise HTTPException(status_code=404, detail="Widget not found")
        
        return widget
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error customizing widget: {e}")
        raise HTTPException(status_code=500, detail="Error customizing widget")


@router.get("/config/default")
async def get_default_widget_config(
    chatbot_id: uuid.UUID,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Get default widget configuration."""
    try:
        config = widget_service.get_default_widget_config()
        
        return APIResponse(
            success=True,
            message="Default widget configuration",
            data=config
        )
        
    except Exception as e:
        logger.error(f"Error getting default config: {e}")
        raise HTTPException(status_code=500, detail="Error getting default configuration")


# Public widget endpoints (no authentication required)
@public_router.get("/by-key/{api_key}")
async def get_widget_by_api_key(
    api_key: str,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Get widget configuration by API key for embedding."""
    try:
        widget = await widget_service.get_widget_by_api_key(api_key)
        if not widget:
            raise HTTPException(status_code=404, detail="Widget not found")

        return APIResponse(
            success=True,
            message="Widget retrieved successfully",
            data=widget
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting widget by API key: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving widget")


@router.post("/validate_access")
async def validate_widget_access(
    chatbot_id: uuid.UUID,
    api_key: str,
    origin: str,
    widget_service: WidgetService = Depends(get_widget_service)
):
    """Validate widget access for a specific origin."""
    try:
        is_allowed, widget = await widget_service.validate_widget_access(api_key, origin)
        
        return APIResponse(
            success=True,
            message="Access validation completed",
            data={
                "allowed": is_allowed,
                "widget_id": str(widget.id) if widget else None,
                "chatbot_id": str(widget.chatbot_id) if widget else None
            }
        )
        
    except Exception as e:
        logger.error(f"Error validating widget access: {e}")
        raise HTTPException(status_code=500, detail="Error validating access")
